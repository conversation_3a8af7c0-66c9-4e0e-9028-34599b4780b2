import os
from flask import Flask, request, render_template, redirect, url_for, flash, send_file, jsonify
from werkzeug.utils import secure_filename
from config import config
from utils.file_processor import FileProcessor
from utils.qr_generator import QRGenerator
from utils.image_merger import ImageMerger
from utils.data_manager import DataManager

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化组件
    file_processor = FileProcessor(
        app.config['UPLOAD_FOLDER'],
        app.config['PROCESSED_FOLDER'],
        app.config['ALLOWED_EXTENSIONS']
    )
    
    qr_generator = QRGenerator(app.config['QR_CODE_SIZE'])
    image_merger = ImageMerger(app.config['QR_CODE_MARGIN'])
    data_manager = DataManager(app.config['URL_MAPPING_FILE'])
    
    @app.route('/')
    def index():
        """首页 - 文件上传页面"""
        return render_template('index.html')
    
    @app.route('/upload', methods=['POST'])
    def upload_file():
        """处理文件上传"""
        try:
            # 检查是否有文件
            if 'file' not in request.files:
                flash('没有选择文件')
                return redirect(url_for('index'))
            
            file = request.files['file']
            if file.filename == '':
                flash('没有选择文件')
                return redirect(url_for('index'))
            
            # 保存上传的文件
            filepath, filename = file_processor.save_uploaded_file(file)
            if not filepath:
                flash('不支持的文件类型')
                return redirect(url_for('index'))
            
            # 确定文件类型
            file_type = file_processor.get_file_type(filename)
            
            # 处理文件
            if file_type == 'pdf':
                # PDF转图片
                processed_path, processed_filename = file_processor.pdf_to_image(filepath)
            elif file_type == 'image':
                # 直接处理图片
                processed_path, processed_filename = file_processor.process_image(filepath)
            else:
                flash('不支持的文件类型')
                return redirect(url_for('index'))
            
            # 生成验证ID和URL
            verification_id = data_manager.generate_verification_id()
            verification_url = f"{app.config['BASE_VERIFY_URL']}/{verification_id}"
            
            # 获取图片信息以确定二维码大小
            img_info = file_processor.get_image_info(processed_path)
            qr_size = qr_generator.get_optimal_size(img_info['width'], img_info['height'])
            
            # 获取用户设置的位置参数
            margin_left = int(request.form.get('margin_left', app.config['QR_CODE_MARGIN']))
            margin_bottom = int(request.form.get('margin_bottom', app.config['QR_CODE_MARGIN']))

            # 生成二维码
            qr_image = qr_generator.generate_qr_code(verification_url, qr_size)

            # 合成图片
            final_filename = f"final_{processed_filename}"
            final_path = os.path.join(app.config['PROCESSED_FOLDER'], final_filename)

            # 使用自定义位置合成图片
            image_merger.merge_qr_with_custom_position(
                processed_path, qr_image, final_path,
                margin_left=margin_left,
                margin_bottom=margin_bottom
            )
            
            # 保存映射信息
            data_manager.save_file_mapping(
                file.filename, final_filename, verification_id, file_type
            )
            
            # 清理临时文件
            file_processor.cleanup_file(filepath)
            if processed_path != final_path:
                file_processor.cleanup_file(processed_path)
            
            return render_template('result.html', 
                                 filename=final_filename,
                                 verification_url=verification_url,
                                 verification_id=verification_id)
        
        except Exception as e:
            flash(f'处理失败: {str(e)}')
            return redirect(url_for('index'))
    
    @app.route('/download/<filename>')
    def download_file(filename):
        """下载处理后的文件"""
        try:
            file_path = os.path.join(app.config['PROCESSED_FOLDER'], filename)
            if os.path.exists(file_path):
                return send_file(file_path, as_attachment=True)
            else:
                flash('文件不存在')
                return redirect(url_for('index'))
        except Exception as e:
            flash(f'下载失败: {str(e)}')
            return redirect(url_for('index'))
    
    @app.route('/<verification_id>')
    def verify_file(verification_id):
        """验证页面"""
        try:
            # 获取文件映射信息
            mapping_info = data_manager.get_file_mapping(verification_id)
            
            if not mapping_info:
                return render_template('verify.html', 
                                     error='验证码无效或已过期')
            
            # 更新访问信息
            data_manager.update_access_info(verification_id)
            
            # 检查文件是否存在
            file_path = os.path.join(app.config['PROCESSED_FOLDER'], 
                                   mapping_info['processed_filename'])
            
            if not os.path.exists(file_path):
                return render_template('verify.html', 
                                     error='文件不存在')
            
            return render_template('verify.html', 
                                 mapping_info=mapping_info,
                                 verification_id=verification_id)
        
        except Exception as e:
            return render_template('verify.html', 
                                 error=f'验证失败: {str(e)}')
    
    @app.route('/view/<verification_id>')
    def view_file(verification_id):
        """查看文件"""
        try:
            mapping_info = data_manager.get_file_mapping(verification_id)

            if not mapping_info:
                return "文件不存在", 404

            file_path = os.path.join(app.config['PROCESSED_FOLDER'],
                                   mapping_info['processed_filename'])

            if os.path.exists(file_path):
                # 检查是否是下载请求
                if request.args.get('download') == '1':
                    return send_file(file_path, as_attachment=True,
                                   download_name=mapping_info['original_filename'])
                else:
                    return send_file(file_path)
            else:
                return "文件不存在", 404

        except Exception as e:
            return f"查看失败: {str(e)}", 500
    
    @app.route('/stats')
    def stats():
        """统计信息（可选的管理功能）"""
        try:
            stats_data = data_manager.get_stats()
            return jsonify(stats_data)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/health')
    def health():
        """健康检查"""
        return jsonify({'status': 'ok', 'service': 'main_app'})

    return app

# 创建应用实例供gunicorn使用
app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=6066, debug=True)
