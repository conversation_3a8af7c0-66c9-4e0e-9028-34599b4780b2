# PDF/图片二维码添加项目 - 部署文档

## 项目概述

这是一个基于Flask的Web应用，用于给PDF文件或图片添加二维码。项目采用Docker容器化部署，支持两个独立的服务：

- **主应用服务** (端口6065): 处理文件上传和二维码添加
- **验证服务** (端口5005): 处理二维码验证和文件查看

## 服务器配置

- **目标服务器**: ************
- **域名映射**:
  - `https://certificate.gaoliming.top` → `http://************:6065`
  - `https://ruxye.gaoliming.top` → `http://************:5005`

## 部署前准备

### 1. 系统要求

- Ubuntu 18.04+ 或 CentOS 7+
- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB RAM
- 至少 10GB 可用磁盘空间

### 2. 安装Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 上传项目文件

将项目文件上传到服务器的 `/opt/pdf-qr-app` 目录：

```bash
sudo mkdir -p /opt/pdf-qr-app
cd /opt/pdf-qr-app
# 上传所有项目文件到此目录
```

## 快速部署

### 1. 使用部署脚本

```bash
cd /opt/pdf-qr-app
sudo ./deploy.sh
```

部署脚本会自动：
- 检查Docker环境
- 创建必要目录
- 构建Docker镜像
- 启动服务
- 进行健康检查

### 2. 手动部署

如果需要手动部署，可以执行以下命令：

```bash
# 创建必要目录
mkdir -p uploads processed data logs
chmod 755 uploads processed data logs

# 构建并启动服务
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 服务验证

部署完成后，可以通过以下方式验证服务：

### 1. 健康检查

```bash
# 检查主应用
curl http://localhost:6065/health

# 检查验证服务
curl http://localhost:5005/health
```

### 2. 功能测试

1. 访问 `http://************:6065` 查看上传页面
2. 上传一个测试图片或PDF文件
3. 检查是否生成了带二维码的结果
4. 扫描二维码验证是否能正确跳转到验证页面

## 服务管理

### 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 更新服务
docker-compose down
docker-compose up --build -d

# 清理旧镜像
docker system prune -f
```

### 日志管理

日志文件位置：
- 容器日志: `./logs/`
- Supervisor日志: `./logs/supervisord.log`
- 主应用日志: `./logs/main_app.log`
- 验证服务日志: `./logs/verify_server.log`

### 数据备份

重要数据目录：
- `./uploads/`: 用户上传的原始文件
- `./processed/`: 处理后的文件
- `./data/`: 验证映射数据

建议定期备份这些目录：

```bash
# 创建备份
tar -czf backup-$(date +%Y%m%d).tar.gz uploads processed data

# 恢复备份
tar -xzf backup-20240101.tar.gz
```

## 故障排除

### 1. 服务无法启动

```bash
# 查看详细日志
docker-compose logs

# 检查端口占用
netstat -tlnp | grep -E '6065|5005'

# 重新构建镜像
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 文件处理失败

检查以下问题：
- 上传文件大小是否超过16MB限制
- 文件格式是否支持（PDF, PNG, JPG, JPEG, GIF）
- 磁盘空间是否充足
- poppler工具是否正确安装

### 3. 二维码无法访问

检查：
- 验证服务是否正常运行
- 域名解析是否正确
- 防火墙设置是否允许5005端口访问

### 4. 性能优化

如果遇到性能问题，可以调整以下配置：

```yaml
# 在docker-compose.yml中调整worker数量
command: gunicorn --bind 0.0.0.0:6065 --workers 4 --timeout 120 app:app
```

## 安全建议

1. **更改默认密钥**:
   ```bash
   # 在docker-compose.yml中设置
   environment:
     - SECRET_KEY=your-secure-random-key-here
   ```

2. **设置防火墙**:
   ```bash
   # 只允许必要端口
   sudo ufw allow 6065
   sudo ufw allow 5005
   sudo ufw enable
   ```

3. **定期更新**:
   ```bash
   # 定期更新Docker镜像
   docker-compose pull
   docker-compose up -d
   ```

4. **监控日志**:
   ```bash
   # 设置日志轮转
   # 在docker-compose.yml中已配置日志大小限制
   ```

## 联系支持

如果遇到部署问题，请检查：
1. 系统日志: `docker-compose logs`
2. 应用日志: `./logs/`目录下的文件
3. 系统资源: `docker stats`

确保提供详细的错误信息以便快速解决问题。
