{% extends "base.html" %}

{% block title %}处理完成 - PDF/图片二维码添加工具{% endblock %}

{% block header %}处理完成{% endblock %}
{% block subtitle %}您的文件已成功添加二维码{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <strong>处理成功！</strong> 已为您的文件添加验证二维码。
        </div>

        <div class="file-info">
            <h5><i class="fas fa-file-image me-2"></i>处理结果</h5>
            <div class="row align-items-center">
                <div class="col-md-8">
                    <p><strong>文件名：</strong>{{ filename }}</p>
                    <p><strong>验证URL：</strong></p>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" value="{{ verification_url }}" id="verificationUrl" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                    </div>
                    <p class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        扫描图片左下角的二维码即可访问此验证页面
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <div class="qr-code-preview">
                        <p><strong>验证二维码预览：</strong></p>
                        <div id="qrCodePreview"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="{{ url_for('download_file', filename=filename) }}" class="btn btn-custom btn-lg me-3">
                <i class="fas fa-download me-2"></i>下载文件
            </a>
            <a href="{{ verification_url }}" target="_blank" class="btn btn-outline-primary btn-lg me-3">
                <i class="fas fa-external-link-alt me-2"></i>查看验证页面
            </a>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-plus me-2"></i>处理新文件
            </a>
        </div>

        <div class="mt-5">
            <h5><i class="fas fa-lightbulb me-2"></i>使用提示</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="p-3">
                        <h6><i class="fas fa-qrcode me-2 text-primary"></i>二维码验证</h6>
                        <p class="text-muted mb-0">使用手机扫描图片左下角的二维码，即可访问验证页面查看原始文件。</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="p-3">
                        <h6><i class="fas fa-link me-2 text-primary"></i>分享链接</h6>
                        <p class="text-muted mb-0">您也可以直接分享验证URL给他人，让他们验证文件的真实性。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 生成二维码预览
    const verificationUrl = "{{ verification_url }}";
    const qrCodeContainer = document.getElementById('qrCodePreview');
    
    QRCode.toCanvas(qrCodeContainer, verificationUrl, {
        width: 150,
        height: 150,
        colorDark: '#000000',
        colorLight: '#ffffff',
        margin: 1
    }, function (error) {
        if (error) {
            console.error(error);
            qrCodeContainer.innerHTML = '<p class="text-muted">二维码生成失败</p>';
        }
    });
});

function copyToClipboard() {
    const urlInput = document.getElementById('verificationUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // 显示复制成功提示
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> 已复制';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
        
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制链接');
    }
}
</script>
{% endblock %}
