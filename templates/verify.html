{% extends "base.html" %}

{% block title %}文件验证 - PDF/图片二维码添加工具{% endblock %}

{% block header %}文件验证{% endblock %}
{% block subtitle %}验证文件的真实性和完整性{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        {% if error %}
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>验证失败！</strong> {{ error }}
            </div>
            <div class="text-center">
                <a href="{{ url_for('index') }}" class="btn btn-custom btn-lg">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
            </div>
        {% else %}
            <div class="alert alert-success" role="alert">
                <i class="fas fa-shield-check me-2"></i>
                <strong>验证成功！</strong> 这是一个有效的验证文件。
            </div>

            <div class="file-info">
                <h5><i class="fas fa-file-check me-2"></i>文件信息</h5>
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>原始文件名：</strong></td>
                                <td>{{ mapping_info.original_filename }}</td>
                            </tr>
                            <tr>
                                <td><strong>文件类型：</strong></td>
                                <td>
                                    {% if mapping_info.file_type == 'pdf' %}
                                        <span class="badge bg-danger">PDF</span>
                                    {% else %}
                                        <span class="badge bg-primary">图片</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>处理时间：</strong></td>
                                <td>{{ mapping_info.created_at[:19].replace('T', ' ') }}</td>
                            </tr>
                            <tr>
                                <td><strong>访问次数：</strong></td>
                                <td>{{ mapping_info.access_count }}</td>
                            </tr>
                            {% if mapping_info.last_accessed %}
                            <tr>
                                <td><strong>最后访问：</strong></td>
                                <td>{{ mapping_info.last_accessed[:19].replace('T', ' ') }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="verification-badge">
                            <i class="fas fa-certificate fa-4x text-success mb-2"></i>
                            <h6 class="text-success">已验证</h6>
                            <small class="text-muted">此文件已通过验证</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="{{ url_for('view_file', verification_id=verification_id) }}" 
                   class="btn btn-custom btn-lg me-3" target="_blank">
                    <i class="fas fa-eye me-2"></i>查看文件
                </a>
                <a href="{{ url_for('view_file', verification_id=verification_id) }}?download=1" 
                   class="btn btn-outline-primary btn-lg me-3">
                    <i class="fas fa-download me-2"></i>下载文件
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-plus me-2"></i>处理新文件
                </a>
            </div>

            <div class="mt-5">
                <h5><i class="fas fa-info-circle me-2"></i>验证说明</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-shield-check fa-2x text-success mb-2"></i>
                            <h6>安全验证</h6>
                            <small class="text-muted">通过唯一验证码确保文件真实性</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-clock fa-2x text-info mb-2"></i>
                            <h6>时间戳记录</h6>
                            <small class="text-muted">记录文件处理和访问时间</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3">
                            <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                            <h6>访问统计</h6>
                            <small class="text-muted">跟踪文件的访问次数</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件预览区域 -->
            <div class="mt-4">
                <h5><i class="fas fa-image me-2"></i>文件预览</h5>
                <div class="text-center">
                    <img src="{{ url_for('view_file', verification_id=verification_id) }}" 
                         class="img-fluid qr-preview" 
                         alt="文件预览"
                         style="max-height: 500px; border: 1px solid #ddd; border-radius: 10px;">
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.verification-badge {
    background: #f8f9ff;
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid #e8f5e8;
}
.table td {
    padding: 0.75rem 0.5rem;
    border: none;
}
.file-info {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
}
</style>
{% endblock %}
