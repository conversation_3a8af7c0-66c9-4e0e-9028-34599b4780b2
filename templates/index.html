{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                    <h4>选择或拖拽文件到此处</h4>
                    <p class="text-muted mb-4">支持 PDF、PNG、JPG、JPEG、GIF 格式，最大 16MB</p>

                    <input type="file" name="file" id="fileInput" class="d-none" accept=".pdf,.png,.jpg,.jpeg,.gif"
                        required>

                    <button type="button" class="btn btn-custom btn-lg"
                        onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-folder-open me-2"></i>选择文件
                    </button>
                </div>

                <div class="file-preview d-none" id="filePreview">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file fa-2x text-primary me-3"></i>
                            <div>
                                <h6 class="mb-1" id="fileName"></h6>
                                <small class="text-muted" id="fileSize"></small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearFile()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 二维码位置设置 -->
            <div class="qr-position-settings mt-4" id="qrPositionSettings" style="display: none;">
                <h5><i class="fas fa-cog me-2"></i>二维码位置设置</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="marginLeft" class="form-label">距离左边缘距离 (像素)</label>
                            <div class="input-group">
                                <input type="range" class="form-range" id="marginLeftRange" min="5" max="100" value="15"
                                    oninput="updateMarginLeft(this.value)">
                                <input type="number" class="form-control" id="marginLeft" name="margin_left" min="5"
                                    max="100" value="15" style="max-width: 80px;"
                                    onchange="updateMarginLeftRange(this.value)">
                                <span class="input-group-text">px</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="marginBottom" class="form-label">距离下边缘距离 (像素)</label>
                            <div class="input-group">
                                <input type="range" class="form-range" id="marginBottomRange" min="5" max="100"
                                    value="15" oninput="updateMarginBottom(this.value)">
                                <input type="number" class="form-control" id="marginBottom" name="margin_bottom" min="5"
                                    max="100" value="15" style="max-width: 80px;"
                                    onchange="updateMarginBottomRange(this.value)">
                                <span class="input-group-text">px</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 二维码预览区域 -->
                <div class="qr-preview-container">
                    <h6><i class="fas fa-eye me-2"></i>位置预览</h6>
                    <div class="position-preview" id="positionPreview">
                        <div class="preview-image">
                            <div class="qr-placeholder" id="qrPlaceholder">
                                <i class="fas fa-qrcode"></i>
                            </div>
                        </div>
                        <small class="text-muted">二维码位置预览（实际大小会根据图片尺寸自动调整）</small>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-custom btn-lg" id="submitBtn" disabled>
                    <i class="fas fa-magic me-2"></i>添加二维码
                </button>
            </div>
        </form>

        <div class="mt-5">
            <h5><i class="fas fa-info-circle me-2"></i>使用说明</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center p-3">
                        <i class="fas fa-upload fa-2x text-primary mb-2"></i>
                        <h6>1. 上传文件</h6>
                        <small class="text-muted">选择PDF或图片文件上传</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3">
                        <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                        <h6>2. 自动处理</h6>
                        <small class="text-muted">系统自动在左下角添加二维码</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3">
                        <i class="fas fa-download fa-2x text-primary mb-2"></i>
                        <h6>3. 下载结果</h6>
                        <small class="text-muted">下载带二维码的处理结果</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const filePreview = document.getElementById('filePreview');
        const uploadContent = document.querySelector('.upload-content');
        const submitBtn = document.getElementById('submitBtn');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        // 拖拽功能
        uploadArea.addEventListener('dragover', function (e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function (e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function (e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // 文件选择
        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                // 检查文件类型
                const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('不支持的文件类型！请选择 PDF 或图片文件。');
                    clearFile();
                    return;
                }

                // 检查文件大小 (16MB)
                if (file.size > 16 * 1024 * 1024) {
                    alert('文件太大！请选择小于 16MB 的文件。');
                    clearFile();
                    return;
                }

                // 显示文件信息
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);

                uploadContent.classList.add('d-none');
                filePreview.classList.remove('d-none');
                document.getElementById('qrPositionSettings').style.display = 'block';
                submitBtn.disabled = false;

                // 初始化二维码位置预览
                updateQRPosition();
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        window.clearFile = function () {
            fileInput.value = '';
            uploadContent.classList.remove('d-none');
            filePreview.classList.add('d-none');
            document.getElementById('qrPositionSettings').style.display = 'none';
            submitBtn.disabled = true;
        };

        // 表单提交时显示加载状态
        document.getElementById('uploadForm').addEventListener('submit', function () {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
            submitBtn.disabled = true;
        });
    });

    // 二维码位置设置函数
    function updateMarginLeft(value) {
        document.getElementById('marginLeft').value = value;
        updateQRPosition();
    }

    function updateMarginLeftRange(value) {
        document.getElementById('marginLeftRange').value = value;
        updateQRPosition();
    }

    function updateMarginBottom(value) {
        document.getElementById('marginBottom').value = value;
        updateQRPosition();
    }

    function updateMarginBottomRange(value) {
        document.getElementById('marginBottomRange').value = value;
        updateQRPosition();
    }

    function updateQRPosition() {
        const marginLeft = parseInt(document.getElementById('marginLeft').value);
        const marginBottom = parseInt(document.getElementById('marginBottom').value);
        const qrPlaceholder = document.getElementById('qrPlaceholder');
        const previewImage = document.querySelector('.preview-image');

        if (qrPlaceholder && previewImage) {
            // 计算在预览区域中的位置（按比例缩放）
            const previewWidth = previewImage.offsetWidth;
            const previewHeight = previewImage.offsetHeight;

            // 将实际像素值转换为预览区域的比例位置
            const leftPercent = (marginLeft / 200) * 100; // 假设最大边距为200px
            const bottomPercent = (marginBottom / 150) * 100; // 假设最大边距为150px

            qrPlaceholder.style.left = Math.min(leftPercent, 85) + '%';
            qrPlaceholder.style.bottom = Math.min(bottomPercent, 85) + '%';
        }
    }
</script>
{% endblock %}