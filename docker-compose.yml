version: '3.8'

services:
  pdf-qr-app:
    build: .
    container_name: pdf-qr-container
    restart: unless-stopped
    ports:
      - "6065:6065"   # certificate.gaoliming.top
      - "5005:5005"   # ruxye.gaoliming.top
    volumes:
      # 持久化数据目录
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6065/health", "&&", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
