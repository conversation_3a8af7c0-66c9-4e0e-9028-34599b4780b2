#!/usr/bin/env python3
"""
验证服务器 - 专门处理验证请求
运行在端口5005，处理来自 https://ruxye.gaoliming.top 的验证请求
"""

import os
import sys
from flask import Flask, request, render_template, send_file, jsonify
from utils.data_manager import DataManager
from config import config

def create_verify_app():
    """创建验证应用"""
    app = Flask(__name__)
    
    # 使用生产环境配置
    app.config.from_object(config['production'])
    config['production'].init_app(app)
    
    # 初始化数据管理器
    data_manager = DataManager(app.config['URL_MAPPING_FILE'])
    
    @app.route('/')
    def index():
        """根路径返回404"""
        return "Not Found", 404
    
    @app.route('/<verification_id>')
    def verify_file(verification_id):
        """直接返回图片文件"""
        try:
            # 获取文件映射信息
            mapping_info = data_manager.get_file_mapping(verification_id)

            if not mapping_info:
                return "文件不存在或验证码无效", 404

            # 更新访问信息
            data_manager.update_access_info(verification_id)

            # 检查文件是否存在
            file_path = os.path.join(app.config['PROCESSED_FOLDER'],
                                   mapping_info['processed_filename'])

            if not os.path.exists(file_path):
                return "文件不存在", 404

            # 直接返回图片文件
            return send_file(file_path, mimetype='image/png')

        except Exception as e:
            return f"访问失败: {str(e)}", 500

    @app.route('/info/<verification_id>')
    def info_page(verification_id):
        """验证信息页面"""
        try:
            # 获取文件映射信息
            mapping_info = data_manager.get_file_mapping(verification_id)

            if not mapping_info:
                return render_template('verify.html',
                                     error='验证码无效或已过期')

            # 更新访问信息
            data_manager.update_access_info(verification_id)

            # 检查文件是否存在
            file_path = os.path.join(app.config['PROCESSED_FOLDER'],
                                   mapping_info['processed_filename'])

            if not os.path.exists(file_path):
                return render_template('verify.html',
                                     error='文件不存在')

            return render_template('verify.html',
                                 mapping_info=mapping_info,
                                 verification_id=verification_id)

        except Exception as e:
            return render_template('verify.html',
                                 error=f'验证失败: {str(e)}')

    @app.route('/view/<verification_id>')
    def view_file(verification_id):
        """查看文件"""
        try:
            mapping_info = data_manager.get_file_mapping(verification_id)

            if not mapping_info:
                return "文件不存在", 404

            file_path = os.path.join(app.config['PROCESSED_FOLDER'],
                                   mapping_info['processed_filename'])

            if os.path.exists(file_path):
                # 检查是否是下载请求
                if request.args.get('download') == '1':
                    return send_file(file_path, as_attachment=True,
                                   download_name=mapping_info['original_filename'])
                else:
                    return send_file(file_path)
            else:
                return "文件不存在", 404

        except Exception as e:
            return f"查看失败: {str(e)}", 500
    
    @app.route('/health')
    def health():
        """健康检查"""
        return jsonify({'status': 'ok', 'service': 'verify_server'})
    
    return app

# 创建应用实例供gunicorn使用
app = create_verify_app()

if __name__ == '__main__':
    print("启动验证服务器...")
    print("访问地址: http://0.0.0.0:5005")
    print("对应域名: https://ruxye.gaoliming.top")
    app.run(host='0.0.0.0', port=5005, debug=False)
