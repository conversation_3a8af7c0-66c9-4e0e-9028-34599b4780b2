import qrcode
from PIL import Image
import io

class QRGenerator:
    """二维码生成器类"""
    
    def __init__(self, size=100):
        self.size = size
    
    def generate_qr_code(self, data, size=None):
        """生成二维码图片"""
        if size is None:
            size = self.size
        
        try:
            # 创建二维码实例
            qr = qrcode.QRCode(
                version=1,  # 控制二维码的大小
                error_correction=qrcode.constants.ERROR_CORRECT_L,  # 错误纠正级别
                box_size=10,  # 每个小格子包含的像素数
                border=0,  # 边框的大小（最小为4）
            )
            
            # 添加数据
            qr.add_data(data)
            qr.make(fit=True)
            
            # 创建二维码图片
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # 调整大小
            qr_img = qr_img.resize((size, size), Image.Resampling.LANCZOS)
            
            return qr_img
            
        except Exception as e:
            raise Exception(f"二维码生成失败: {str(e)}")
    
    def generate_qr_bytes(self, data, size=None):
        """生成二维码字节数据"""
        qr_img = self.generate_qr_code(data, size)
        
        # 转换为字节
        img_bytes = io.BytesIO()
        qr_img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        return img_bytes.getvalue()
    
    def save_qr_code(self, data, output_path, size=None):
        """保存二维码到文件"""
        qr_img = self.generate_qr_code(data, size)
        qr_img.save(output_path, 'PNG')
        return output_path
    
    def get_optimal_size(self, target_image_width, target_image_height, ratio=0.1):
        """根据目标图片尺寸计算最佳二维码大小"""
        # 二维码大小为图片较小边的10%（默认）
        min_dimension = min(target_image_width, target_image_height)
        optimal_size = int(min_dimension * ratio)
        
        # 确保最小尺寸
        return max(optimal_size, 50)
    
    def create_transparent_qr(self, data, size=None):
        """创建带透明背景的二维码"""
        if size is None:
            size = self.size
        
        try:
            # 创建二维码实例
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=0,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # 创建二维码图片
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为RGBA模式以支持透明度
            qr_img = qr_img.convert("RGBA")
            
            # 将白色背景设为透明
            data = qr_img.getdata()
            new_data = []
            for item in data:
                # 如果是白色（或接近白色），设为透明
                if item[0] > 200 and item[1] > 200 and item[2] > 200:
                    new_data.append((255, 255, 255, 0))  # 透明
                else:
                    new_data.append(item)
            
            qr_img.putdata(new_data)
            
            # 调整大小
            qr_img = qr_img.resize((size, size), Image.Resampling.LANCZOS)
            
            return qr_img
            
        except Exception as e:
            raise Exception(f"透明二维码生成失败: {str(e)}")
