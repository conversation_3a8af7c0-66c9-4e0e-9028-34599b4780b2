from PIL import Image
import os

class ImageMerger:
    """图片合成器类"""
    
    def __init__(self, margin=10):
        self.margin = margin
    
    def merge_qr_with_image(self, base_image_path, qr_image, output_path, position='bottom-left', margin=None):
        """将二维码合成到基础图片上"""
        if margin is None:
            margin = self.margin
        
        try:
            # 打开基础图片
            with Image.open(base_image_path) as base_img:
                # 确保基础图片是RGB模式
                if base_img.mode != 'RGB':
                    base_img = base_img.convert('RGB')
                
                # 创建一个副本用于编辑
                result_img = base_img.copy()
                
                # 获取图片尺寸
                base_width, base_height = result_img.size
                qr_width, qr_height = qr_image.size
                
                # 计算二维码位置
                x, y = self._calculate_position(
                    base_width, base_height,
                    qr_width, qr_height,
                    position, margin
                )
                
                # 如果二维码有透明通道，使用paste with mask
                if qr_image.mode == 'RGBA':
                    result_img.paste(qr_image, (x, y), qr_image)
                else:
                    result_img.paste(qr_image, (x, y))
                
                # 保存结果
                result_img.save(output_path, 'PNG')
                
                return output_path
                
        except Exception as e:
            raise Exception(f"图片合成失败: {str(e)}")

    def merge_qr_with_custom_position(self, base_image_path, qr_image, output_path, margin_left=15, margin_bottom=15):
        """将二维码合成到基础图片上（自定义左下角位置）"""
        try:
            # 打开基础图片
            with Image.open(base_image_path) as base_img:
                # 确保基础图片是RGB模式
                if base_img.mode != 'RGB':
                    base_img = base_img.convert('RGB')

                # 创建一个副本用于编辑
                result_img = base_img.copy()

                # 获取图片尺寸
                base_width, base_height = result_img.size
                qr_width, qr_height = qr_image.size

                # 计算二维码位置（左下角定位）
                x = margin_left
                y = base_height - qr_height - margin_bottom

                # 确保二维码不会超出图片边界
                x = max(0, min(x, base_width - qr_width))
                y = max(0, min(y, base_height - qr_height))

                # 如果二维码有透明通道，使用paste with mask
                if qr_image.mode == 'RGBA':
                    result_img.paste(qr_image, (x, y), qr_image)
                else:
                    result_img.paste(qr_image, (x, y))

                # 保存结果
                result_img.save(output_path, 'PNG')

                return output_path

        except Exception as e:
            raise Exception(f"自定义位置图片合成失败: {str(e)}")

    def _calculate_position(self, base_width, base_height, qr_width, qr_height, position, margin):
        """计算二维码在基础图片上的位置"""
        positions = {
            'top-left': (margin, margin),
            'top-right': (base_width - qr_width - margin, margin),
            'bottom-left': (margin, base_height - qr_height - margin),
            'bottom-right': (base_width - qr_width - margin, base_height - qr_height - margin),
            'center': ((base_width - qr_width) // 2, (base_height - qr_height) // 2)
        }
        
        if position not in positions:
            position = 'bottom-left'  # 默认位置
        
        return positions[position]
    
    def create_composite_image(self, base_image_path, qr_code_data, qr_size, output_path, 
                             position='bottom-left', margin=None):
        """创建合成图片（一步完成）"""
        from .qr_generator import QRGenerator
        
        if margin is None:
            margin = self.margin
        
        try:
            # 生成二维码
            qr_generator = QRGenerator(qr_size)
            qr_image = qr_generator.generate_qr_code(qr_code_data, qr_size)
            
            # 合成图片
            return self.merge_qr_with_image(base_image_path, qr_image, output_path, position, margin)
            
        except Exception as e:
            raise Exception(f"合成图片创建失败: {str(e)}")
    
    def add_qr_with_background(self, base_image_path, qr_image, output_path, 
                             position='bottom-left', margin=None, bg_color='white', bg_padding=5):
        """添加带背景的二维码"""
        if margin is None:
            margin = self.margin
        
        try:
            # 打开基础图片
            with Image.open(base_image_path) as base_img:
                if base_img.mode != 'RGB':
                    base_img = base_img.convert('RGB')
                
                result_img = base_img.copy()
                
                # 创建带背景的二维码
                qr_with_bg = self._create_qr_with_background(qr_image, bg_color, bg_padding)
                
                # 获取尺寸
                base_width, base_height = result_img.size
                qr_bg_width, qr_bg_height = qr_with_bg.size
                
                # 计算位置
                x, y = self._calculate_position(
                    base_width, base_height,
                    qr_bg_width, qr_bg_height,
                    position, margin
                )
                
                # 粘贴二维码
                result_img.paste(qr_with_bg, (x, y))
                
                # 保存结果
                result_img.save(output_path, 'PNG')
                
                return output_path
                
        except Exception as e:
            raise Exception(f"带背景二维码合成失败: {str(e)}")
    
    def _create_qr_with_background(self, qr_image, bg_color, padding):
        """为二维码创建背景"""
        qr_width, qr_height = qr_image.size
        bg_width = qr_width + 2 * padding
        bg_height = qr_height + 2 * padding
        
        # 创建背景图片
        bg_img = Image.new('RGB', (bg_width, bg_height), bg_color)
        
        # 将二维码粘贴到背景上
        bg_img.paste(qr_image, (padding, padding))
        
        return bg_img
    
    def resize_image_if_needed(self, image_path, max_width=2000, max_height=2000):
        """如果图片太大则调整大小"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                
                # 检查是否需要调整大小
                if width <= max_width and height <= max_height:
                    return image_path  # 不需要调整
                
                # 计算新尺寸（保持宽高比）
                ratio = min(max_width / width, max_height / height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)
                
                # 调整大小
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 保存调整后的图片
                resized_img.save(image_path, 'PNG')
                
                return image_path
                
        except Exception as e:
            raise Exception(f"图片大小调整失败: {str(e)}")
