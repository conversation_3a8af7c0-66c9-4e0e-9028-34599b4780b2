import os
import uuid
from PIL import Image
from pdf2image import convert_from_path
from werkzeug.utils import secure_filename

class FileProcessor:
    """文件处理器类"""
    
    def __init__(self, upload_folder, processed_folder, allowed_extensions):
        self.upload_folder = upload_folder
        self.processed_folder = processed_folder
        self.allowed_extensions = allowed_extensions
    
    def allowed_file(self, filename):
        """检查文件扩展名是否允许"""
        if '.' not in filename:
            return False

        parts = filename.rsplit('.', 1)
        if len(parts) < 2:
            return False

        return parts[1].lower() in self.allowed_extensions
    
    def save_uploaded_file(self, file):
        """保存上传的文件"""
        if file and self.allowed_file(file.filename):
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            # 添加UUID前缀避免文件名冲突
            unique_filename = f"{uuid.uuid4().hex}_{filename}"
            filepath = os.path.join(self.upload_folder, unique_filename)
            file.save(filepath)
            return filepath, unique_filename
        return None, None
    
    def get_file_type(self, filename):
        """获取文件类型"""
        ext = filename.rsplit('.', 1)[1].lower()
        if ext == 'pdf':
            return 'pdf'
        elif ext in ['png', 'jpg', 'jpeg', 'gif']:
            return 'image'
        return 'unknown'
    
    def pdf_to_image(self, pdf_path, output_folder=None):
        """将PDF转换为图片"""
        if output_folder is None:
            output_folder = self.processed_folder
        
        try:
            # 转换PDF为图片列表
            images = convert_from_path(pdf_path, dpi=300)

            if not images or len(images) == 0:
                raise ValueError("PDF转换失败：无法生成图片")

            # 只处理第一页
            first_page = images[0]
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(pdf_path))[0]
            output_filename = f"{base_name}_page1.png"
            output_path = os.path.join(output_folder, output_filename)
            
            # 保存图片
            first_page.save(output_path, 'PNG')
            
            return output_path, output_filename
            
        except Exception as e:
            raise Exception(f"PDF转图片失败: {str(e)}")
    
    def process_image(self, image_path, output_folder=None):
        """处理图片文件"""
        if output_folder is None:
            output_folder = self.processed_folder
        
        try:
            # 打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 生成输出文件名
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_filename = f"{base_name}_processed.png"
                output_path = os.path.join(output_folder, output_filename)
                
                # 保存处理后的图片
                img.save(output_path, 'PNG')
                
                return output_path, output_filename
                
        except Exception as e:
            raise Exception(f"图片处理失败: {str(e)}")
    
    def get_image_info(self, image_path):
        """获取图片信息"""
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format
                }
        except Exception as e:
            raise Exception(f"获取图片信息失败: {str(e)}")
    
    def cleanup_file(self, filepath):
        """清理临时文件"""
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
        except Exception:
            pass
        return False
