import json
import os
import uuid
from datetime import datetime

class DataManager:
    """数据管理器类"""
    
    def __init__(self, data_file_path):
        self.data_file_path = data_file_path
        self._ensure_data_file()
    
    def _ensure_data_file(self):
        """确保数据文件存在"""
        if not os.path.exists(self.data_file_path):
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(self.data_file_path), exist_ok=True)
            # 创建空的数据文件
            self._save_data({})
    
    def _load_data(self):
        """加载数据"""
        try:
            with open(self.data_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_data(self, data):
        """保存数据"""
        try:
            with open(self.data_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"数据保存失败: {str(e)}")
    
    def generate_verification_id(self):
        """生成验证ID"""
        return uuid.uuid4().hex[:12]  # 12位随机字符串
    
    def save_file_mapping(self, original_filename, processed_filename, verification_id, file_type):
        """保存文件映射信息"""
        data = self._load_data()
        
        mapping_info = {
            'original_filename': original_filename,
            'processed_filename': processed_filename,
            'file_type': file_type,
            'created_at': datetime.now().isoformat(),
            'access_count': 0,
            'last_accessed': None
        }
        
        data[verification_id] = mapping_info
        self._save_data(data)
        
        return verification_id
    
    def get_file_mapping(self, verification_id):
        """获取文件映射信息"""
        data = self._load_data()
        return data.get(verification_id)
    
    def update_access_info(self, verification_id):
        """更新访问信息"""
        data = self._load_data()
        
        if verification_id in data:
            data[verification_id]['access_count'] += 1
            data[verification_id]['last_accessed'] = datetime.now().isoformat()
            self._save_data(data)
            return True
        
        return False
    
    def delete_mapping(self, verification_id):
        """删除映射信息"""
        data = self._load_data()
        
        if verification_id in data:
            del data[verification_id]
            self._save_data(data)
            return True
        
        return False
    
    def get_all_mappings(self):
        """获取所有映射信息"""
        return self._load_data()
    
    def cleanup_old_mappings(self, days=30):
        """清理旧的映射信息"""
        from datetime import datetime, timedelta
        
        data = self._load_data()
        cutoff_date = datetime.now() - timedelta(days=days)
        
        to_delete = []
        for verification_id, info in data.items():
            try:
                created_at = datetime.fromisoformat(info['created_at'])
                if created_at < cutoff_date:
                    to_delete.append(verification_id)
            except (ValueError, KeyError):
                # 如果日期格式有问题，也删除
                to_delete.append(verification_id)
        
        for verification_id in to_delete:
            del data[verification_id]
        
        if to_delete:
            self._save_data(data)
        
        return len(to_delete)
    
    def get_stats(self):
        """获取统计信息"""
        data = self._load_data()
        
        total_files = len(data)
        total_accesses = sum(info.get('access_count', 0) for info in data.values())
        
        file_types = {}
        for info in data.values():
            file_type = info.get('file_type', 'unknown')
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        return {
            'total_files': total_files,
            'total_accesses': total_accesses,
            'file_types': file_types
        }
