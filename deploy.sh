#!/bin/bash

# PDF QR码应用部署脚本

set -e

echo "=== PDF QR码应用 Docker 部署 ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker Compose是否可用
if ! docker compose version &> /dev/null; then
    echo "错误: Docker Compose 不可用，请检查Docker安装"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p uploads processed data logs

# 设置目录权限
chmod 755 uploads processed data logs

# 停止现有容器（如果存在）
echo "停止现有容器..."
sudo docker compose down || true

# 构建并启动容器
echo "构建并启动容器..."
sudo docker compose up --build -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
sudo docker compose ps

# 检查健康状态
echo "检查应用健康状态..."
for i in {1..30}; do
    if curl -f http://localhost:6065/health > /dev/null 2>&1; then
        echo "✅ 主应用启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 主应用启动失败"
        sudo docker compose logs pdf-qr-app
        exit 1
    fi
    echo "等待主应用启动... ($i/30)"
    sleep 2
done

# 检查验证服务
for i in {1..30}; do
    if curl -f http://localhost:5005/health > /dev/null 2>&1; then
        echo "✅ 验证服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 验证服务启动失败"
        sudo docker compose logs pdf-qr-app
        exit 1
    fi
    echo "等待验证服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 部署完成！"
echo ""
echo "服务访问地址："
echo "  主应用: http://localhost:6065 (对应 https://certificate.gaoliming.top)"
echo "  验证服务: http://localhost:5005 (对应 https://ruxye.gaoliming.top)"
echo ""
echo "管理命令："
echo "  查看日志: sudo docker compose logs -f"
echo "  停止服务: sudo docker compose down"
echo "  重启服务: sudo docker compose restart"
echo "  查看状态: sudo docker compose ps"
echo ""
echo "数据持久化目录："
echo "  上传文件: ./uploads"
echo "  处理文件: ./processed"
echo "  数据文件: ./data"
echo "  日志文件: ./logs"
