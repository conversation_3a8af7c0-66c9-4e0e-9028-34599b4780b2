[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid
user=root

[program:main_app]
command=gunicorn --bind 0.0.0.0:6065 --workers 2 --timeout 120 app:app
directory=/app
autostart=true
autorestart=true
stderr_logfile=/app/logs/main_app_error.log
stdout_logfile=/app/logs/main_app.log
environment=FLASK_ENV=production

[program:verify_server]
command=gunicorn --bind 0.0.0.0:5005 --workers 2 --timeout 120 verify_server:app
directory=/app
autostart=true
autorestart=true
stderr_logfile=/app/logs/verify_server_error.log
stdout_logfile=/app/logs/verify_server.log
environment=FLASK_ENV=production

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[unix_http_server]
file=/tmp/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
