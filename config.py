import os
from datetime import timedelta

class Config:
    """应用配置类"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 文件上传配置
    UPLOAD_FOLDER = 'uploads'
    PROCESSED_FOLDER = 'processed'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
    
    # 数据存储配置
    DATA_FOLDER = 'data'
    URL_MAPPING_FILE = os.path.join(DATA_FOLDER, 'url_mapping.json')
    
    # 二维码配置
    QR_CODE_SIZE = 100  # 二维码大小（像素）
    QR_CODE_POSITION = 'bottom-left'  # 二维码位置
    QR_CODE_MARGIN = 10  # 二维码边距
    
    # URL配置
    BASE_VERIFY_URL = 'https://ruxye.gaoliming.top'
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 确保必要的目录存在
        for folder in [Config.UPLOAD_FOLDER, Config.PROCESSED_FOLDER, Config.DATA_FOLDER]:
            if not os.path.exists(folder):
                os.makedirs(folder)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    BASE_VERIFY_URL = 'https://ruxye.gaoliming.top'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
