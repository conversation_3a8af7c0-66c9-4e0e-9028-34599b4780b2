# PDF/图片二维码添加项目

## 项目概述
这是一个基于Flask的Web应用，用于给PDF文件或图片添加二维码。用户可以上传PDF或图片文件，系统会在左下角添加一个二维码，并生成一个验证URL。

## 功能特性
- ✅ 支持PDF和图片文件上传（PDF、PNG、JPG、JPEG、GIF）
- ✅ 自动在左下角添加二维码（无边框、无白边）
- ✅ 生成随机验证URL
- ✅ 支持扫码验证功能
- ✅ 使用Flask单体架构
- ✅ 支持Docker容器化部署
- ✅ 双服务架构（主应用+验证服务）

## 系统架构

### 服务架构
- **主应用服务** (端口6065): 处理文件上传、二维码添加、结果展示
- **验证服务** (端口5005): 处理二维码验证、文件查看

### 域名映射
- `https://certificate.gaoliming.top` → `http://************:6065` (主应用)
- `https://ruxye.gaoliming.top` → `http://************:5005` (验证服务)

## 项目结构
```
pdf_qr_project/
├── app.py                      # Flask主应用
├── verify_server.py            # 验证服务器
├── config.py                   # 配置文件
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── file_processor.py       # 文件处理模块
│   ├── qr_generator.py         # 二维码生成模块
│   ├── image_merger.py         # 图片合成模块
│   └── data_manager.py         # 数据管理模块
├── templates/                  # HTML模板
│   ├── base.html              # 基础模板
│   ├── index.html             # 上传页面
│   ├── result.html            # 结果页面
│   └── verify.html            # 验证页面
├── static/                     # 静态文件目录
├── uploads/                    # 上传文件存储
├── processed/                  # 处理后文件存储
├── data/                       # 数据存储
│   └── url_mapping.json        # URL映射数据
├── logs/                       # 日志文件
├── Dockerfile                  # Docker配置
├── docker-compose.yml          # Docker Compose配置
├── docker-entrypoint.sh        # Docker启动脚本
├── supervisord.conf            # Supervisor配置
├── deploy.sh                   # 部署脚本
├── requirements.txt            # Python依赖
├── DEPLOYMENT.md               # 部署文档
└── README.md                   # 项目说明
```

## 技术栈
- **后端**: Python 3.10, Flask 3.1.1
- **图片处理**: Pillow 11.3.0
- **二维码生成**: qrcode 8.2
- **PDF处理**: pdf2image 1.17.0, poppler-utils
- **Web服务器**: Gunicorn 21.2.0
- **进程管理**: Supervisor 4.2.5
- **容器化**: Docker, Docker Compose

## 快速开始

### 开发环境
```bash
# 创建conda环境
conda create -n pdf_qr_project python=3.9 -y
conda activate pdf_qr_project

# 安装依赖
pip install -r requirements.txt
conda install -c conda-forge poppler -y

# 启动开发服务器
python app.py
```

### Docker部署
```bash
# 快速部署
sudo ./deploy.sh

# 或手动部署
sudo docker compose up --build -d

# 查看服务状态
sudo docker compose ps

# 查看日志
sudo docker compose logs -f
```

## 使用流程

1. **文件上传**: 访问主应用，上传PDF或图片文件
2. **自动处理**: 系统自动处理文件并在左下角添加二维码
3. **获取结果**: 下载处理后的文件，获得验证URL
4. **扫码验证**: 扫描二维码或访问验证URL查看原始文件

## 部署信息
- **目标服务器**: ************
- **主应用端口**: 6065
- **验证服务端口**: 5005
- **数据持久化**: uploads/, processed/, data/, logs/

## 管理命令
```bash
# 查看服务状态
sudo docker compose ps

# 查看实时日志
sudo docker compose logs -f

# 重启服务
sudo docker compose restart

# 停止服务
sudo docker compose down

# 更新部署
sudo docker compose down
sudo docker compose up --build -d
```

## 测试
```bash
# 测试Docker部署
python test_docker_deployment.py

# 健康检查
curl http://localhost:6065/health
curl http://localhost:5005/health
```

## 项目完成状态

✅ **已完成的功能**:
- 项目需求分析和技术栈确认
- 开发环境搭建（conda + Python依赖）
- 项目结构设计
- 核心功能模块开发（文件处理、二维码生成、图片合成）
- Flask Web应用开发（双服务架构）
- 数据存储设计（JSON文件存储）
- 本地开发测试（功能验证通过）
- Docker化配置（Dockerfile + docker-compose.yml）
- 部署配置和文档（完整的部署文档）

🎯 **项目特点**:
- 完全符合用户需求规格
- 使用Village committee certificate.png作为测试样例
- 支持PDF转高清图片
- 二维码无边框无白边
- 验证URL跳转到ruxye.gaoliming.top域名
- 生产环境Docker部署就绪

## 联系信息
项目已完成开发和测试，可直接部署到生产环境。
